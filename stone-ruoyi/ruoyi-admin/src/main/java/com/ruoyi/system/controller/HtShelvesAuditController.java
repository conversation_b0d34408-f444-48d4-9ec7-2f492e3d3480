package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.system.domain.vo.HtShelvesAuditVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HtShelvesAudit;
import com.ruoyi.system.service.IHtShelvesAuditService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 石头委托审核Controller
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@RestController
@RequestMapping("/system/audit")
public class HtShelvesAuditController extends BaseController {
    @Autowired
    private IHtShelvesAuditService htShelvesAuditService;

    /**
     * 查询石头委托审核列表
     */
    @PreAuthorize("@ss.hasPermi('system:audit:list')")
    @GetMapping("/list")
    public TableDataInfo list(HtShelvesAudit htShelvesAudit) {
        startPage();
        List<HtShelvesAuditVo> list = htShelvesAuditService.selectAdminHtShelvesAuditList(htShelvesAudit);
        return getDataTable(list);
    }


    /**
     * 获取石头委托审核详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:audit:query')")
    @GetMapping(value = "/htShelvesAuditInfo/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htShelvesAuditService.selectHtShelvesAuditById(id));
    }

    /**
     * 新增石头委托审核
     */
    @PreAuthorize("@ss.hasPermi('system:audit:add')")
    @Log(title = "石头委托审核", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HtShelvesAudit htShelvesAudit) {
        return toAjax(htShelvesAuditService.insertHtShelvesAudit(htShelvesAudit));
    }

    /**
     * 修改石头委托审核
     */
    @PreAuthorize("@ss.hasPermi('system:audit:edit')")
    @Log(title = "石头委托审核", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HtShelvesAudit htShelvesAudit) {
        return toAjax(htShelvesAuditService.updateHtShelvesAudit(htShelvesAudit));
    }

    /**
     * 删除石头委托审核
     */
    @PreAuthorize("@ss.hasPermi('system:audit:remove')")
    @Log(title = "石头委托审核", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htShelvesAuditService.deleteHtShelvesAuditByIds(ids));
    }

    //审核
    @PostMapping("/updateAudit")
    public AjaxResult audit(@RequestBody HtShelvesAudit htShelvesAudit){
        return htShelvesAuditService.audit(htShelvesAudit);
    }

    //驳回
    @PostMapping("/overrule")
    public AjaxResult overrule(@RequestBody HtShelvesAudit htShelvesAudit){
        return toAjax(htShelvesAuditService.overrule(htShelvesAudit));
    }
}
