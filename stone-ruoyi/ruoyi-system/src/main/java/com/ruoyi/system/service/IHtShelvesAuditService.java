package com.ruoyi.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.HtShelvesAudit;
import com.ruoyi.system.domain.vo.HtShelvesAuditDto;
import com.ruoyi.system.domain.vo.HtShelvesAuditVo;

/**
 * 石头委托审核Service接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface IHtShelvesAuditService extends IService<HtShelvesAudit> {
    /**
     * 查询石头委托审核
     *
     * @param id 石头委托审核主键
     * @return 石头委托审核
     */
    public HtShelvesAudit selectHtShelvesAuditById(Long id);

    /**
     * 查询石头委托审核列表
     *
     * @param htShelvesAudit 石头委托审核
     * @return 石头委托审核集合
     */
    public List<HtShelvesAudit> selectHtShelvesAuditList(HtShelvesAudit htShelvesAudit);

    /**
     * 新增石头委托审核
     *
     * @param htShelvesAudit 石头委托审核
     * @return 结果
     */
    public int insertHtShelvesAudit(HtShelvesAudit htShelvesAudit);

    /**
     * 修改石头委托审核
     *
     * @param htShelvesAudit 石头委托审核
     * @return 结果
     */
    public int updateHtShelvesAudit(HtShelvesAudit htShelvesAudit);

    /**
     * 批量删除石头委托审核
     *
     * @param ids 需要删除的石头委托审核主键集合
     * @return 结果
     */
    public int deleteHtShelvesAuditByIds(Long[] ids);

    /**
     * 删除石头委托审核信息
     *
     * @param id 石头委托审核主键
     * @return 结果
     */
    public int deleteHtShelvesAuditById(Long id);
    /**
     * 石头委托寄售发布
     *
     * @param htShelvesAudit 石头委托信息
     * @return 结果
     */
    public AjaxResult commissionedConsignmentRelease(HtShelvesAuditDto htShelvesAudit);

    List<HtShelvesAuditVo> selectAdminHtShelvesAuditList(HtShelvesAudit htShelvesAudit);

    /**
     * 石头委托信息审核
     *
     * @param htShelvesAudit 石头委托信息
     * @return 结果
     */
    AjaxResult audit(HtShelvesAudit htShelvesAudit);

    int overrule(HtShelvesAudit htShelvesAudit);
}

