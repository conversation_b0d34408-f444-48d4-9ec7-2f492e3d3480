(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-login-login"],{"0b33":function(t,n,e){var o=e("db74");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=e("967d").default;i("145cb6cf",o,!0,{sourceMap:!1,shadowMode:!1})},"105e":function(t,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){}));var o=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",{staticClass:"container"},[e("v-uni-view",{staticClass:"header"},[e("v-uni-image",{staticClass:"logo",attrs:{src:"/static/icon/biglogo.jpg"}})],1),"account"===t.currentTab?e("v-uni-view",{staticClass:"form"},[e("v-uni-view",[t._v("用户名：")]),e("v-uni-input",{staticClass:"input",attrs:{placeholder:"请输入账号"},model:{value:t.form.phone,callback:function(n){t.$set(t.form,"phone",n)},expression:"form.phone"}}),e("v-uni-view",[t._v("密 码：")]),e("v-uni-input",{staticClass:"input",attrs:{type:"password",placeholder:"请输入密码"},model:{value:t.form.password,callback:function(n){t.$set(t.form,"password",n)},expression:"form.password"}}),e("v-uni-label",{staticStyle:{padding:"20rpx 0"}},[e("v-uni-checkbox",{attrs:{checked:t.setcpflag},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.setcp.apply(void 0,arguments)}}}),e("v-uni-text",[t._v("记住密码")])],1),e("v-uni-button",{staticClass:"login-btn",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.login.apply(void 0,arguments)}}},[t._v("登录")])],1):"phone"===t.currentTab?e("v-uni-view",{staticClass:"form"},[e("v-uni-input",{staticClass:"input",attrs:{placeholder:"请输入手机号"},model:{value:t.form.phone,callback:function(n){t.$set(t.form,"phone",n)},expression:"form.phone"}}),e("v-uni-view",{staticClass:"code-input"},[e("v-uni-input",{staticClass:"input",attrs:{placeholder:"请输入验证码"},model:{value:t.form.code,callback:function(n){t.$set(t.form,"code",n)},expression:"form.code"}}),e("v-uni-button",{staticClass:"send-code-btn",attrs:{disabled:t.countdown>0},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.sendCode.apply(void 0,arguments)}}},[t._v(t._s(t.countdown>0?t.countdown+"秒后重新发送":"发送验证码"))])],1),e("v-uni-button",{staticClass:"login-btn",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.login.apply(void 0,arguments)}}},[t._v("登录")])],1):t._e(),t._e(),e("v-uni-view",{staticClass:"agreement"},[e("v-uni-text",[t._v("登录即表示同意")]),e("v-uni-text",{staticClass:"link",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.viewTerms.apply(void 0,arguments)}}},[t._v("《会员协议》")]),e("v-uni-text",[t._v("与")]),e("v-uni-text",{staticClass:"link",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.viewPrivacy.apply(void 0,arguments)}}},[t._v("《隐私政策》")])],1)],1)},i=[]},"1a5a":function(t,n,e){"use strict";e.r(n);var o=e("105e"),i=e("d22b");for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(a);e("df92d");var s=e("828b"),c=Object(s["a"])(i["default"],o["b"],o["c"],!1,null,"24096fa2",null,!1,o["a"],void 0);n["default"]=c.exports},d22b:function(t,n,e){"use strict";e.r(n);var o=e("f178"),i=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(a);n["default"]=i.a},db74:function(t,n,e){var o=e("c86c");n=o(!1),n.push([t.i,".container[data-v-24096fa2]{height:100%;padding:%?20?%;background-color:#f5f5f5;display:flex;flex-direction:column;align-items:center}.header[data-v-24096fa2]{display:flex;flex-direction:column;align-items:center;margin-bottom:%?30?%}.logo[data-v-24096fa2]{width:100vw;height:20vh}.title[data-v-24096fa2]{font-size:%?36?%;margin-top:%?20?%}.tab-switch[data-v-24096fa2]{display:flex;justify-content:center;margin-bottom:%?20?%}.tab-switch uni-button[data-v-24096fa2]{margin:0 %?10?%;padding:%?10?% %?20?%;border:none;background-color:#ccc;color:#fff;font-size:%?28?%;border-radius:%?10?%}.tab-switch .active[data-v-24096fa2]{background-color:#3cb371}.form[data-v-24096fa2]{width:80%;display:flex;flex-direction:column}.input[data-v-24096fa2]{margin-bottom:%?20?%;padding:%?20?%;border:%?1?% solid #ddd;border-radius:%?10?%;font-size:%?28?%}.code-input[data-v-24096fa2]{display:flex;justify-content:space-between}.send-code-btn[data-v-24096fa2]{margin-left:%?10?%;background-color:#3cb371;color:#fff;border-radius:%?10?%;width:%?250?%;height:%?80?%;font-size:%?30?%;line-height:%?80?%}.login-btn[data-v-24096fa2]{width:100%;padding:%?20?%;background-color:#3cb371;color:#fff;border:none;border-radius:%?10?%;font-size:%?28?%}.quick-login[data-v-24096fa2]{margin:%?20?% 0;text-align:center}.wechat-login-btn[data-v-24096fa2]{margin-top:%?10?%;padding:%?20?%;background-color:#1aad19;color:#fff;border-radius:%?10?%;font-size:%?28?%}.agreement[data-v-24096fa2]{text-align:center;margin-top:%?30?%;position:fixed;bottom:10%;font-size:%?26?%;display:flex;flex-direction:row;align-items:center}.link[data-v-24096fa2]{color:#3cb371;margin:0 %?5?%}.link_btn[data-v-24096fa2]{display:inline;height:%?50?%;width:%?140?%;display:inline-block;background-color:green;font-size:%?24?%;line-height:%?50?%;color:#fff;border-radius:%?20?%}",""]),t.exports=n},df92d:function(t,n,e){"use strict";var o=e("0b33"),i=e.n(o);i.a},f178:function(t,n,e){"use strict";e("6a54");var o=e("f5bd").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=o(e("9b1b")),a=e("8f59"),s={data:function(){return{currentTab:"account",countdown:0,form:{password:"",phone:"",code:""},setcpflag:!1}},onLoad:function(){this.getsavedata()},onShow:function(){this.getsavedata()},methods:(0,i.default)((0,i.default)({switchTab:function(t){this.currentTab=t}},(0,a.mapMutations)(["setUserCountKey"])),{},{login:function(){if(console.log(this.form),"account"===this.currentTab){if(!this.form.phone||!this.form.password)return uni.showToast({title:"请输入账号和密码",icon:"none"});var t={phone:this.form.phone,password:this.form.password,loginType:"1"};this.apiEvent(t)}else if("phone"===this.currentTab){if(!this.form.phone||!this.form.code)return uni.showToast({title:"请输入手机号和验证码",icon:"none"});var n={phone:this.form.phone,phoneCode:this.form.code,loginType:"2"};this.apiEvent(n),console.log("手机号验证码登录",this.form.phone,this.form.code)}},apiEvent:function(t){var n=this;this.$api.request({url:this.$api.tologinbycp,method:"POST",data:t}).then((function(e){console.log(e,"返回");var o=e.appUser,i={id:o.id,headImg:o.headImg,nickName:o.nickName,phone:o.phone,realName:o.realName,nameAuth:o.nameAuth};uni.setStorageSync("user_count_key",e.token),uni.setStorageSync("userInfo",i),n.setUserCountKey(e.token),n.savecp(t),uni.showToast({title:"登陆成功"}),setTimeout((function(){uni.switchTab({url:"/pages/home/<USER>"})}),1200)}))},sendCode:function(){var t=this;if(!this.form.phone)return uni.showToast({title:"请输入手机号",icon:"none"});this.$api.request({url:this.$api.getdecode+"?phone="+this.form.phone}).then((function(t){console.log(t,"验证码")})),console.log("发送验证码到",this.form.phone),this.countdown=60;var n=setInterval((function(){t.countdown>0?t.countdown--:clearInterval(n)}),1e3)},wechatLogin:function(){console.log("微信快捷登录"),uni.showToast({title:"微信登录成功",icon:"success"})},viewTerms:function(){uni.navigateTo({url:"/pages/terms/terms"})},viewPrivacy:function(){uni.navigateTo({url:"/pages/privacy/privacy"})},viewRegister:function(){uni.navigateTo({url:"/pages/login/register"})},setcp:function(){this.setcpflag=!this.setcpflag,uni.setStorageSync("setcpflag",this.setcpflag),console.log(this.setcpflag)},savecp:function(t){var n=uni.getStorageSync("setcpflag");n?uni.setStorageSync("savedata",t):uni.removeStorageSync("savedata")},getsavedata:function(){var t=uni.getStorageSync("setcpflag");if(t){this.setcpflag=t;var n=uni.getStorageSync("savedata");this.form.phone=n.phone,this.form.password=n.password}}})};n.default=s}}]);